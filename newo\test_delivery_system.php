<?php

/**
 * ملف اختبار نظام التوصيل
 * 
 * هذا الملف يحتوي على اختبارات لنظام التوصيل الجديد
 */

require_once 'vendor/autoload.php';

use App\Models\Customer;
use App\Models\Pos;
use App\Models\User;
use App\Models\FinancialRecord;

echo "=== اختبار نظام التوصيل ===\n\n";

// 1. اختبار إنشاء عميل بصلاحية توصيل
echo "1. اختبار إنشاء عميل بصلاحية توصيل:\n";
try {
    $customer = Customer::where('is_delivery', 1)->first();
    if ($customer) {
        echo "✅ تم العثور على عميل بصلاحية توصيل: {$customer->name}\n";
    } else {
        echo "❌ لم يتم العثور على عميل بصلاحية توصيل\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

// 2. اختبار إنشاء فاتورة توصيل
echo "\n2. اختبار إنشاء فاتورة توصيل:\n";
try {
    $deliveryPos = Pos::where('delivery_status', 'delivery_pending')->first();
    if ($deliveryPos) {
        echo "✅ تم العثور على فاتورة توصيل قيد الانتظار: POS #{$deliveryPos->id}\n";
        echo "   - حالة التوصيل: {$deliveryPos->delivery_status}\n";
        echo "   - الكاشير المسؤول: " . ($deliveryPos->cashier ? $deliveryPos->cashier->name : 'غير محدد') . "\n";
    } else {
        echo "❌ لم يتم العثور على فواتير توصيل قيد الانتظار\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

// 3. اختبار الكاشيرز
echo "\n3. اختبار الكاشيرز:\n";
try {
    $cashiers = User::whereHas('roles', function($query) {
        $query->where('name', 'Cashier');
    })->get();
    
    if ($cashiers->count() > 0) {
        echo "✅ تم العثور على {$cashiers->count()} كاشير:\n";
        foreach ($cashiers as $cashier) {
            echo "   - {$cashier->name} (ID: {$cashier->id})\n";
        }
    } else {
        echo "❌ لم يتم العثور على كاشيرز\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

// 4. اختبار السجلات المالية
echo "\n4. اختبار السجلات المالية:\n";
try {
    $financialRecords = FinancialRecord::whereNotNull('delivery_cash')->get();
    
    if ($financialRecords->count() > 0) {
        echo "✅ تم العثور على {$financialRecords->count()} سجل مالي يحتوي على delivery_cash:\n";
        foreach ($financialRecords as $record) {
            echo "   - Shift ID: {$record->shift_id}, Delivery Cash: {$record->delivery_cash}\n";
        }
    } else {
        echo "❌ لم يتم العثور على سجلات مالية تحتوي على delivery_cash\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

// 5. اختبار حالات التوصيل
echo "\n5. اختبار حالات التوصيل:\n";
try {
    $deliveryStatuses = Pos::$deliveryStatuses;
    echo "✅ حالات التوصيل المتاحة:\n";
    foreach ($deliveryStatuses as $key => $value) {
        $count = Pos::where('delivery_status', $key)->count();
        echo "   - {$value} ({$key}): {$count} فاتورة\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}

echo "\n=== انتهى الاختبار ===\n";
